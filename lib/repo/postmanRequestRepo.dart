import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;
import 'package:postman_flutter/api_constant.dart';
import 'package:postman_flutter/model/request_details_model.dart';
import 'package:postman_flutter/model/request_model.dart';


class PostmanRequestRepository {
  Future<PostmanRequestResponse> createRequest({
    required String name,
    required String url,
    required String method,
    String? collectionId,
    String? folderId,
    required int workspaceId,
  }) async {
    final apiUrl = Uri.parse(ApiConstant.createRequest);

    final params = {
      'name': name,
      'url': url,
      'method': method,
      'workspace_id': workspaceId.toString(),
    };

    if (collectionId != null) {
      params['collection_id'] = collectionId;
    } else if (folderId != null) {
      params['folder_id'] = folderId;
    }

    try {
      debugPrint('Creating request with params: $params');
      final response = await http.post(
        apiUrl,
        headers: ApiHeaders.headers,
        body: jsonEncode(params),
      ).timeout(const Duration(seconds: 30));

      debugPrint('Create request response: ${response.body}');

      if (response.statusCode == 200) {
        final jsonResponse = json.decode(response.body);
        return PostmanRequestResponse.fromJson(jsonResponse);
      } else {
        //return PostmanRequestResponse.error('Failed to create request. Status code: ${response.statusCode}');
        return PostmanRequestResponse(msg: "Failed to create request. Status code: ${response.statusCode}", status: "error",);
      }
    } catch (e) {
      debugPrint('Error creating request: $e');
      return PostmanRequestResponse(msg: "Error creating request: $e", status: "error",);
    }
  }

  Future<PostmanRequestResponse> getRequestById({required int requestId}) async {
    final apiUrl = Uri.parse('${ApiConstant.getRequestDetails}$requestId');

    try {
      debugPrint('Fetching request details for ID: $requestId');
      final response = await http.get(
        apiUrl,
        headers: ApiHeaders.headers,
      ).timeout(const Duration(seconds: 30));

      debugPrint('Get request details response: ${response.body}');

      if (response.statusCode == 200) {
        final jsonResponse = json.decode(response.body);
        return PostmanRequestResponse.fromJson(jsonResponse);
      } else {
        return PostmanRequestResponse(
          msg: "Failed to fetch request details. Status code: ${response.statusCode}",
          status: "error",
        );
      }
    } catch (e) {
      debugPrint('Error fetching request details: $e');
      return PostmanRequestResponse(
        msg: "Error fetching request details: $e",
        status: "error",
      );
    }
  }

  Future<Map<String, dynamic>> sendRequest({
    required String method,
    required String url,
    Map<String, String>? headers,
    Map<String, dynamic>? body,
    String? tabUuid,
  }) async {
    final apiUrl = Uri.parse(ApiConstant.sendRequest);

    final Map<String, String> safeHeaders = headers ?? {};

    debugPrint('Headers before sending: $safeHeaders');

    final requestData = {
      'method': method,
      'url': url,
      'headers': safeHeaders,
      'body': body ?? {},
      'tabUuid': tabUuid,
      'request_id': ApiConstant.requestID,
      'user_id': ApiConstant.userID,
    };

    try {
      //debugPrint('Sending request with params: ${jsonEncode(requestData)}');
      final response = await http.post(
        apiUrl,
        headers: ApiHeaders.headers,
        body: jsonEncode(requestData),
      ).timeout(const Duration(seconds: 60));

      //debugPrint('Send request response: ${response.body}');

      if (response.statusCode == 200) {
        final jsonResponse = json.decode(response.body);
        return jsonResponse;
      } else {
        return {
          'status': 'error',
          'msg': 'Failed to send request. Status code: ${response.statusCode}',
          'data': null,
        };
      }
    } catch (e) {
      debugPrint('Error sending request: $e');
      return {
        'status': 'error',
        'msg': 'Error sending request: $e',
        'data': null,
      };
    }
  }

  Future<Map<String, dynamic>> saveRequest({
    required String name,
    required String url,
    required String method,
    required Map<String, String> headers,
    required dynamic body,
    required Map<String, dynamic> params,
    required Map<String, dynamic> auth,
    String? collectionId,
  }) async {
    final apiUrl = Uri.parse('${ApiConstant.saveRequest}${ApiConstant.requestID}');

    debugPrint('Headers before saving: $headers');

    final requestData = {
      'name': name,
      'url': url,
      'method': method,
      'headers': headers,
      'body': body,
      'params': params,
      'auth': auth,
      'collection_id': collectionId,
    };

    if (collectionId != null) {
      requestData['collection_id'] = collectionId;
    }

    try {
      debugPrint('Saving request with params: ${jsonEncode(requestData)}');
      final response = await http.post(
        apiUrl,
        headers: ApiHeaders.headers,
        body: jsonEncode(requestData),
      ).timeout(const Duration(seconds: 30));

      debugPrint('Save request response: ${response.body}');

      if (response.statusCode == 200) {
        final jsonResponse = json.decode(response.body);
        return jsonResponse;
      } else {
        return {
          'status': 'error',
          'message': 'Failed to save request. Status code: ${response.statusCode}',
          'data': null,
        };
      }
    } catch (e) {
      debugPrint('Error saving request: $e');
      return {
        'status': 'error',
        'message': 'Error saving request: $e',
        'data': null,
      };
    }
  }

  Future<RequestDetailsResponse> getRequestDetails({
    required int requestId,
  }) async {
    final apiUrl = Uri.parse('${ApiConstant.getRequestDetails}$requestId');

    try {
      debugPrint('Fetching request details for ID: $requestId');
      final response = await http.get(
        apiUrl,
        headers: ApiHeaders.headers,
      ).timeout(const Duration(seconds: 30));

      debugPrint('Get request details response: ${response.body}');

      if (response.statusCode == 200) {
        final jsonResponse = json.decode(response.body);
        return RequestDetailsResponse.fromJson(jsonResponse);
      } else {
        return RequestDetailsResponse(
          status: 'error',
          message: 'Failed to fetch request details. Status code: ${response.statusCode}',
        );
      }
    } catch (e) {
      debugPrint('Error fetching request details: $e');
      return RequestDetailsResponse(
        status: 'error',
        message: 'Error fetching request details: $e',
      );
    }
  }
}
