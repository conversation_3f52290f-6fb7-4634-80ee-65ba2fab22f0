import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_code_editor/flutter_code_editor.dart';
import 'package:multi_split_view/multi_split_view.dart';
import 'package:postman_flutter/api_constant.dart';
import 'package:postman_flutter/features/home/<USER>/home_state.dart';
import 'package:postman_flutter/features/home/<USER>/postmanrequest/postman_request_bloc.dart';
import 'package:postman_flutter/features/home/<USER>/postmanrequest/postman_request_event.dart';
import 'package:postman_flutter/features/home/<USER>/postmanrequest/postman_request_state.dart';
import 'package:postman_flutter/features/home/<USER>/authorization/authorization.dart';
import 'package:postman_flutter/features/home/<USER>/authorization/bloc/auth_subviews_bloc.dart';
import 'package:postman_flutter/features/home/<USER>/authorization/bloc/auth_subviews_event.dart';
import 'package:postman_flutter/features/home/<USER>/body/body.dart';
import 'package:postman_flutter/features/home/<USER>/body/form_data/bloc/form_data_bloc.dart';
import 'package:postman_flutter/features/home/<USER>/body/form_data/bloc/form_data_event.dart';
import 'package:postman_flutter/features/home/<USER>/body/json_data/bloc/json_data_bloc.dart';
import 'package:postman_flutter/features/home/<USER>/body/json_data/bloc/json_data_event.dart';
import 'package:postman_flutter/features/home/<USER>/header/bloc/headers_bloc.dart';
import 'package:postman_flutter/features/home/<USER>/header/bloc/headers_event.dart';
import 'package:postman_flutter/features/home/<USER>/header/headers_view.dart';
import 'package:postman_flutter/features/home/<USER>/query_params/bloc/query_params_bloc.dart';
import 'package:postman_flutter/features/home/<USER>/query_params/bloc/query_params_event.dart';
import 'package:postman_flutter/features/home/<USER>/query_params/query_params_view.dart';
import 'package:postman_flutter/features/home/<USER>/response_view.dart';
import 'package:postman_flutter/features/home/<USER>/scripts.dart';
import 'package:postman_flutter/features/home/<USER>/send_request/request_sender_view.dart';
import 'package:postman_flutter/features/home/<USER>/settings.dart';
import 'package:postman_flutter/features/common_widgets/dynamic_radio_tabs/bloc/tab_bloc.dart' as radio_tab;
import 'package:postman_flutter/features/common_widgets/dynamic_radio_tabs/bloc/tab_event.dart' as radio_tab_event;
import 'package:postman_flutter/helpers/color_config.dart';
import 'package:postman_flutter/helpers/common.dart';
import 'package:uuid/uuid.dart';
import '../../globals.dart';
import '../../helpers/style_config.dart';
import '../common_widgets/CommonAlertDialog.dart';
import '../common_widgets/CommonButton.dart';
import '../common_widgets/CommonIconTextButton.dart';
import '../common_widgets/CommonSVGIcon.dart';
import '../common_widgets/CommonText.dart';
import '../common_widgets/CommonTextButton.dart';
import '../common_widgets/CommonTextFormField.dart';
import '../common_widgets/common_dropdown.dart';
import '../common_widgets/common_snackbar.dart';
import '../common_widgets/dynamic_tabs_widget.dart';
import 'bloc/home_bloc.dart';
import 'bloc/home_event.dart';
import 'data/api_collection.dart';
import 'data/hive_db_provider.dart';

enum AreaType { topPanel, bottomPanel }

class TabManager extends ValueNotifier<Object?> {
  final List<TabModel> openTabs = []; // Store TabModel objects
  TabModel? activeTab;

  TabManager() : super(null);

  void openTab(TabModel tab) {
    // Check if the tab already exists
    final existingTabIndex = openTabs.indexWhere((t) => t.uuid == tab.uuid);

    if (existingTabIndex != -1) {
      // Switch to the existing tab
      activeTab = openTabs[existingTabIndex];
    } else {
      // Add the new tab and switch to it
      openTabs.add(tab);
      activeTab = tab;
    }

    notifyListeners();
  }

  void closeTab(String uuid) {
    openTabs.removeWhere((tab) => tab.uuid == uuid);

    if (activeTab?.uuid == uuid) {
      activeTab = openTabs.isNotEmpty ? openTabs.last : null;
    }

    notifyListeners();
  }

  void switchTab(String uuid) {
    final tab = openTabs.firstWhere((tab) => tab.uuid == uuid, orElse: () => throw Exception('Tab not found'));
    activeTab = tab;
    notifyListeners();
  }
}


class TabView extends StatefulWidget {
  @override
  State<TabView> createState() => _TabViewState();
}

class _TabViewState extends State<TabView> with AutomaticKeepAliveClientMixin {
  final MultiSplitViewController _verticalController = MultiSplitViewController();
  final ScrollController _scrollControllerCollection = ScrollController();
  final ScrollController _scrollControllerFolder = ScrollController();
  final ScrollController _scrollControllerTabbar = ScrollController();
  final ScrollController scrollControllerTabbar = ScrollController();
  late HomeBloc homeBloc;

  // Keep this widget alive when it's not visible
  @override
  bool get wantKeepAlive => true;

  @override
  void initState() {
    super.initState();
    _verticalController.areas = [
      Area(flex: 1, min: 0.5),
      Area(flex: 1, min: 0.4),
    ];
    homeBloc = BlocProvider.of<HomeBloc>(context,listen: false);
    WidgetsBinding.instance.addPostFrameCallback((_){
      homeBloc.add(LoadApiDataEvent());
    });
  }

  void _scrollToNewTab() {
    final scrollController = ScrollControllerProvider.of(context)?.scrollControllerTabbar;

    Future.delayed(Duration(milliseconds: 300), () {
      if (scrollController != null && scrollController.hasClients) {
        scrollController.animateTo(
          scrollController.position.maxScrollExtent,
          duration: Duration(milliseconds: 500),
          curve: Curves.easeOut,
        );
      }
    });
  }

  bool _isTabModified(TabModel tab) {
    if (tab.originalTabName == null && tab.originalMethod == null &&
        tab.originalUrl == null && tab.originalJsonData == null && tab.originalHeaders == null) {
      return false;
    }

    final tabNameMatches = tab.tabName == (tab.originalTabName ?? tab.tabName);
    final methodMatches = tab.method == (tab.originalMethod ?? tab.method);
    final urlMatches = tab.url == (tab.originalUrl ?? tab.url);
    final jsonDataMatches = _mapsEqual(tab.jsonData, tab.originalJsonData);
    final headersMatches = _mapsEqual(tab.headers, tab.originalHeaders);

    final isModified = !(
      tab.tabName == (tab.originalTabName ?? tab.tabName) &&
      tab.method == (tab.originalMethod ?? tab.method) &&
      tab.url == (tab.originalUrl ?? tab.url) &&
      _mapsEqual(tab.jsonData, tab.originalJsonData) &&
      _mapsEqual(tab.headers, tab.originalHeaders)
    );
    debugPrint('tab.jsonData:' + tab.jsonData.toString() + ' tab.originalJsonData:' + tab.originalJsonData.toString() + ')');
    debugPrint('Tab ${tab.uuid} modification check:');
    debugPrint('  - TabName: ${tab.tabName} vs ${tab.originalTabName} = $tabNameMatches');
    debugPrint('  - Method: ${tab.method} vs ${tab.originalMethod} = $methodMatches');
    debugPrint('  - URL: ${tab.url} vs ${tab.originalUrl} = $urlMatches');
    debugPrint('  - JsonData matches: $jsonDataMatches');
    debugPrint('  - Headers matches: $headersMatches');
    debugPrint('  - Final isModified: $isModified');

    return isModified;
  }


  // bool _mapsEqual<K, V>(Map<K, V>? map1, Map<K, V>? map2) {
  //   if (map1 == null && map2 == null) return true;
  //   if (map1 == null || map2 == null) return false;
  //   if (map1.length != map2.length) return false;

  //   for (final key in map1.keys) {
  //     if (!map2.containsKey(key) || map1[key] != map2[key]) {
  //       return false;
  //     }
  //   }
  //   return true;
  // }

  bool _deepEquals(dynamic a, dynamic b) {
    if (a is Map && b is Map) {
      if (a.length != b.length) return false;
      for (final key in a.keys) {
        if (!b.containsKey(key) || !_deepEquals(a[key], b[key])) {
          return false;
        }
      }
      return true;
    } else if (a is List && b is List) {
      if (a.length != b.length) return false;
      for (int i = 0; i < a.length; i++) {
        if (!_deepEquals(a[i], b[i])) return false;
      }
      return true;
    } else {
      return a == b;
    }
  }

  bool _mapsEqual<K, V>(Map<K, V>? map1, Map<K, V>? map2) {
    if (map1 == null && map2 == null) return true;
    if (map1 == null || map2 == null) return false;
    return _deepEquals(map1, map2);
  }

  @override
  Widget build(BuildContext context) {
    super.build(context); // Must call super.build for AutomaticKeepAliveClientMixin

    return BlocBuilder<HomeBloc, HomeState>(
      buildWhen: (previous, current) {
        // Always rebuild if the number of tabs changes
        if (previous.openTabs.length != current.openTabs.length) {
          debugPrint('TabManager: Rebuilding due to tab count change: ${previous.openTabs.length} -> ${current.openTabs.length}');
          return true;
        }

        // Always rebuild if the active tab changes
        if (previous.activeTab?.uuid != current.activeTab?.uuid) {
          debugPrint('TabManager: Rebuilding due to active tab change: ${previous.activeTab?.uuid} -> ${current.activeTab?.uuid}');
          return true;
        }

        // Rebuild if the active tab's properties change
        if (previous.activeTab != null && current.activeTab != null &&
            previous.activeTab!.uuid == current.activeTab!.uuid) {
          final shouldRebuild = previous.activeTab!.url != current.activeTab!.url ||
                 previous.activeTab!.method != current.activeTab!.method ||
                 previous.activeTab!.tabName != current.activeTab!.tabName ||
                 !_mapsEqual(previous.activeTab!.jsonData, current.activeTab!.jsonData) ||
                 !_mapsEqual(previous.activeTab!.headers, current.activeTab!.headers) ||
                 !_mapsEqual(previous.activeTab!.originalJsonData, current.activeTab!.originalJsonData) ||
                 !_mapsEqual(previous.activeTab!.originalHeaders, current.activeTab!.originalHeaders) ||
                 previous.activeTab!.originalTabName != current.activeTab!.originalTabName ||
                 previous.activeTab!.originalMethod != current.activeTab!.originalMethod ||
                 previous.activeTab!.originalUrl != current.activeTab!.originalUrl;

          if (shouldRebuild) {
            debugPrint('TabManager: Rebuilding due to active tab data change for tab ${current.activeTab!.uuid}');
            debugPrint('  - URL: ${previous.activeTab!.url} -> ${current.activeTab!.url}');
            debugPrint('  - Method: ${previous.activeTab!.method} -> ${current.activeTab!.method}');
            debugPrint('  - TabName: ${previous.activeTab!.tabName} -> ${current.activeTab!.tabName}');
            debugPrint('  - JsonData changed: ${!_mapsEqual(previous.activeTab!.jsonData, current.activeTab!.jsonData)}');
            debugPrint('  - Headers changed: ${!_mapsEqual(previous.activeTab!.headers, current.activeTab!.headers)}');
          }

          return shouldRebuild;
        }

        // Rebuild if any tab's data changes that could affect modification status
        for (int i = 0; i < current.openTabs.length && i < previous.openTabs.length; i++) {
          final prevTab = previous.openTabs[i];
          final currTab = current.openTabs[i];

          if (prevTab.uuid == currTab.uuid) {
            // Check if any data that affects modification status has changed
            final dataChanged = prevTab.url != currTab.url ||
                               prevTab.method != currTab.method ||
                               prevTab.tabName != currTab.tabName ||
                               !_mapsEqual(prevTab.jsonData, currTab.jsonData) ||
                               !_mapsEqual(prevTab.headers, currTab.headers) ||
                               !_mapsEqual(prevTab.originalJsonData, currTab.originalJsonData) ||
                               !_mapsEqual(prevTab.originalHeaders, currTab.originalHeaders) ||
                               prevTab.originalTabName != currTab.originalTabName ||
                               prevTab.originalMethod != currTab.originalMethod ||
                               prevTab.originalUrl != currTab.originalUrl;

            if (dataChanged) {
              debugPrint('TabManager: Rebuilding due to data change for tab ${currTab.uuid}');
              return true;
            }
          }
        }

        return false;
      },
      builder: (context, state) {
        final activeTab = state.activeTab;
        //print('active tab uuid :::: ${activeTab?.headers}');
        if (state.openTabs.isEmpty) {
          return Column(
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.start,
                children: [
                  Padding(
                    padding: const EdgeInsets.all(5.0),
                    child: InkWell(
                      onTap: () {
                        context.read<HomeBloc>().add(
                          OpenTabEvent(
                            tabModel: TabModel(
                              uuid: Uuid().v4(),
                              tabName: "New Request",
                              method: "GET",
                              url: "",
                              jsonData: {},
                              collectionName: '',
                              folderName: '',
                              requestId: null,
                            ),
                          ),
                        );
                      },
                      child: Container(
                        padding: EdgeInsets.all(5),
                        child: Icon(
                          Icons.add,
                          color: Colors.white70,
                          size: 16,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
              Expanded(
                child: Center(
                  child: CommonText(
                    text: "No tabs open",
                    textStyle: mMediumTextStyle16(
                      textSize: 12,
                      textColor: AppThemeColor.white,
                    ),
                  ),
                ),
              ),
            ],
          );
        }

        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Tab Bar
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Expanded(
                  child: Container(
                    margin: EdgeInsets.zero,
                    padding: EdgeInsets.zero,
                    color: AppThemeColor.commonBackground,
                    child: Row(
                      children: [
                        Flexible(
                          child: SingleChildScrollView(
                            scrollDirection: Axis.horizontal,
                            controller: _scrollControllerTabbar,
                            child: Row(
                              children: state.openTabs.map((tab) {
                                bool isActive = tab.uuid == activeTab?.uuid;
                                return GestureDetector(
                                  onTap: () {
                                    if (tab.uuid != activeTab?.uuid) {
                                      Future.microtask(() {
                                        if (mounted) {
                                          context.read<HomeBloc>().add(SwitchTabEvent(tab.uuid));
                                        }
                                      });
                                    }
                                  },
                                  child: Container(
                                    margin: EdgeInsets.only(left: 1, right: 1, top: 0, bottom: 0),
                                    padding: const EdgeInsets.symmetric(vertical: 10, horizontal: 10),
                                    decoration: BoxDecoration(
                                      color: isActive ? AppThemeColor.tabbarBgColor : AppThemeColor.tabbarBgColor,
                                      border: isActive
                                          ? const Border(
                                        bottom: BorderSide(
                                          color: AppThemeColor.tabbarUnderLineColor,
                                          width: 2,
                                        ),
                                      )
                                          : null,
                                    ),
                                    child: Row(
                                      children: [
                                        CommonText(
                                          text: tab.method,
                                          textStyle: mRegularTextStyle16(
                                            textSize: 12,
                                            textColor: AppThemeColor.white,
                                          ),
                                        ),
                                        SizedBox(width: 10),
                                        Row(
                                          children: [
                                            CommonText(
                                              text: tab.tabName,
                                              maxLength: 15,
                                              textStyle: mMediumTextStyle16(
                                                textSize: 12,
                                                textColor: AppThemeColor.tabbarTextColor,
                                              ),
                                            ),

                                            if (_isTabModified(tab)) ...[
                                              const SizedBox(width: 3),
                                              Container(
                                                width: 6,
                                                height: 6,
                                                decoration: const BoxDecoration(
                                                  color: Colors.orange,
                                                  shape: BoxShape.circle,
                                                ),
                                              ),
                                            ],
                                          ],
                                        ),
                                        const SizedBox(width: 5),

                                        MouseRegion(
                                          cursor: SystemMouseCursors.click,
                                          child: GestureDetector(
                                            onTap: () {
                                              final activeTab = context.read<HomeBloc>().state.activeTab;
                                              if (activeTab != null && _isTabModified(activeTab)) {
                                                // Show the close tab alert dialog if the tab has unsaved changes
                                                CommonCloseTabAlertDialog.show(
                                                  context: context,
                                                  title: 'Unsaved Changes',
                                                  buttonCreateText: 'Save',
                                                  description: 'You have unsaved changes. Do you want to save them before closing?',
                                                  inputFields: [],
                                                  onCancel: () {
                                                    Navigator.of(context).pop();
                                                  },
                                                  onSaveChanges: () {
                                                    Navigator.of(context).pop();
                                                    debugPrint('Save button clicked ====> ${activeTab.requestId}');
                                                    // Check if the request is already saved
                                                    if(!isCheckEmpty(activeTab.requestId)) {
                                                      saveRequestToDB(activeTab, context);
                                                    }else{
                                                      //showSaveRequestDialog(context, activeTab, context.read<HomeBloc>().state.apiData);
                                                    }
                                                  },
                                                  onDontSave: () {
                                                    Navigator.of(context).pop();
                                                    context.read<HomeBloc>().add(CloseTabEvent(tab.uuid)); // Close the tab
                                                  },
                                                );
                                              } else {
                                                // Close the tab directly if there are no unsaved changes
                                                context.read<HomeBloc>().add(CloseTabEvent(tab.uuid));
                                              }
                                            },
                                            child: Icon(
                                              Icons.close,
                                              color: Colors.white70,
                                              size: 16,
                                            ),
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                );
                              }).toList(),
                            ),
                          ),
                        ),
                        InkWell(
                          onTap: () {
                            context.read<HomeBloc>().add(
                              OpenTabEvent(
                                tabModel: TabModel(
                                  uuid: Uuid().v4(),
                                  tabName: "New Request ${state.openTabs.length}",
                                  method: "GET",
                                  url: "",
                                  jsonData: {},
                                  responseJsonData: {},
                                 // headers: {},
                                  collectionName: '',
                                  folderName: '',
                                  requestId: null,
                                ),
                              ),
                            );
                            Future.delayed(Duration(milliseconds: 300), () {
                              if (_scrollControllerTabbar.hasClients) {
                                _scrollControllerTabbar.animateTo(
                                  _scrollControllerTabbar.position.maxScrollExtent,
                                  duration: Duration(milliseconds: 500),
                                  curve: Curves.easeOut,
                                );
                              }
                            });

                          },
                          child: Container(
                            padding: EdgeInsets.all(5),
                            child: Icon(
                              Icons.add,
                              color: Colors.white70,
                              size: 16,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),

            // Tab Content
            Expanded(
              child: Container(
                color: AppThemeColor.commonBackground,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Container(
                      height: 50,
                      padding: EdgeInsets.only(left: 20, right: 20),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          CommonText(
                            text: activeTab?.tabName ?? '',
                            textStyle: mMediumTextStyle16(
                              textSize: 14,
                              textColor: AppThemeColor.white,
                            ),
                          ),
                          BlocListener<PostmanRequestBloc, PostmanRequestState>(
                            listener: (context, state) {
                              if (state is RequestSaved && state.tabUuid == activeTab?.uuid) {

                                ScaffoldMessenger.of(context).showSnackBar(
                                  SnackBar(
                                    content: Text(state.response['message'] ?? 'Request saved successfully'),
                                    backgroundColor: Colors.green,
                                  ),
                                );

                                final updatedTab = activeTab!.withOriginalState();
                                context.read<HomeBloc>().add(
                                  UpdateTabEvent(
                                    activeTab.uuid,
                                    tabName: updatedTab.tabName,
                                    method: updatedTab.method,
                                    url: updatedTab.url,
                                    headers: updatedTab.headers,
                                    jsonData: updatedTab.jsonData,
                                    originalTabName: updatedTab.originalTabName,
                                    originalMethod: updatedTab.originalMethod,
                                    originalUrl: updatedTab.originalUrl,
                                    originalJsonData: updatedTab.originalJsonData,
                                    originalHeaders: updatedTab.originalHeaders,
                                    isModified: false,
                                  ),
                                );
                              } else if (state is RequestSaveError && state.tabUuid == activeTab?.uuid) {
                                ScaffoldMessenger.of(context).showSnackBar(
                                  SnackBar(
                                    content: Text(state.message),
                                    backgroundColor: Colors.red,
                                  ),
                                );
                              } else if (state is RequestDetailsFetched) {
                                print('RequestDetailsFetched called in TabManager');
                                final requestDetails = state.requestDetails;
                                final homeBloc = context.read<HomeBloc>();
                                final headersBloc = context.read<HeadersBloc>();
                                final currentActiveTab = homeBloc.state.activeTab;
                                if (currentActiveTab != null) {
                                  Map<String, String> headers = {};
                                  if (requestDetails.headers.isNotEmpty) {
                                    requestDetails.headers.forEach((key, value) {
                                      if (value is String) {
                                        headers[key] = value;
                                      } else {
                                        headers[key] = value.toString();
                                      }
                                    });
                                  }

                                  // Load headers into HeadersBloc for the current tab
                                  debugPrint('Loading headers into HeadersBloc for tab ${currentActiveTab.uuid}: $headers');
                                  headersBloc.add(LoadHeadersEvent(headers, tabUuid: currentActiveTab.uuid));

                                  Map<String, dynamic> jsonData = {};
                                  if (requestDetails.body.type == 'json') {
                                    try {
                                      jsonData = requestDetails.body.getContentAsJson();
                                    } catch (e) {
                                      debugPrint('Error parsing JSON body: $e');
                                    }
                                  }
                                  // Update tab with fetched data and set original state
                                  final updatedTab = currentActiveTab.copyWith(
                                    tabName: requestDetails.name,
                                    method: requestDetails.method,
                                    url: requestDetails.url,
                                    headers: headers,
                                    jsonData: jsonData,
                                    isModified: false,
                                  ).withOriginalState(); // Set current state as original

                                  homeBloc.add(
                                    UpdateTabEvent(
                                      currentActiveTab.uuid,
                                      tabName: updatedTab.tabName,
                                      method: updatedTab.method,
                                      url: updatedTab.url,
                                      headers: updatedTab.headers,
                                      jsonData: updatedTab.jsonData,
                                      originalTabName: updatedTab.originalTabName,
                                      originalMethod: updatedTab.originalMethod,
                                      originalUrl: updatedTab.originalUrl,
                                      originalJsonData: updatedTab.originalJsonData,
                                      originalHeaders: updatedTab.originalHeaders,
                                      isModified: false,
                                    ),
                                  );
                                }
                              } else if (state is RequestDetailsFetchError) {
                                ScaffoldMessenger.of(context).showSnackBar(
                                  SnackBar(
                                    content: Text(state.message),
                                    backgroundColor: Colors.red,
                                  ),
                                );
                              }
                            },
                            child: CommonTextButton(
                              text: 'Save',
                              enabled: activeTab != null ? _isTabModified(activeTab) : false,
                              textStyle: const TextStyle(color: AppThemeColor.textButtonBlue),
                              callback: () {
                                print('activeTab?.requestId while saving.... ${activeTab?.requestId}');
                                if(!isCheckEmpty(activeTab?.requestId)) {
                                  saveRequestToDB(activeTab, context);
                                }else{
                                  
                                }
                                
                                // if (activeTab != null) {
                                //   final headersBloc = context.read<HeadersBloc>();
                                //   final headers = headersBloc.state.rows.where((row) => row.isSelected && row.key.isNotEmpty).fold<Map<String, String>>({}, (map, row) {
                                //     map[row.key] = row.value;
                                //     return map;
                                //   });

                                //   final bodyData = {
                                //     "type": "json",
                                //     "content": jsonEncode(activeTab.jsonData)
                                //   };

                                //   print('bodyData while saving: $bodyData');


                                //   context.read<PostmanRequestBloc>().add(
                                //     SaveRequest(
                                //       name: activeTab.tabName,
                                //       url: activeTab.url ?? "",
                                //       method: activeTab.method,
                                //       headers: headers,
                                //       body: bodyData,
                                //       params: {}, // Empty params for now
                                //       auth: {"type": "none"}, // Default auth type
                                //       collectionId: CommonConstants.collectionID.toString(),
                                //       tabUuid: activeTab.uuid,
                                //     ),
                                //   );
                                // }
                              },
                            ),
                          ),
                        ],
                      ),
                    ),

                    RequestSenderView(
                      uuid: activeTab?.uuid ?? '',
                    ),

                    Expanded(
                      child: MultiSplitViewTheme(
                        data: MultiSplitViewThemeData(
                          dividerThickness: 2,
                          dividerPainter: DividerPainters.background(color: AppThemeColor.dividerBackgroundColor),
                        ),
                        child: MultiSplitView(
                          axis: Axis.vertical,
                          controller: _verticalController,
                          builder: (context, area) {
                            final index = _verticalController.areas.indexOf(area);
                            if (index == 0) {
                              print('hello............${activeTab?.uuid}');
                              return Container(
                                padding: const EdgeInsets.only(left: 20, right: 0, bottom: 0),
                                child: DynamicTabsWidget(
                                  tabTitles: ['Params', 'Authorization', 'Headers', 'Body'],
                                  tabViews: [
                                    QueryParamsView(),
                                    Authorization(),
                                    HeadersView(
                                      key: ValueKey(activeTab?.uuid ?? ''),
                                      uuid: activeTab?.uuid ?? '',
                                      tabModel: activeTab,
                                    ),
                                    Body(
                                      uuid: activeTab?.uuid ?? '',
                                      tabModel: activeTab,
                                    ),
                                  ],
                                ),
                              );
                            } else if (index == 1) {
                              return Container(
                                padding: EdgeInsets.only(left: 20, top: 10, bottom: 0),
                                child: ResponseView(
                                  key: ValueKey(activeTab?.uuid ?? ''),
                                  uuid: activeTab?.uuid ?? '',
                                ),
                              );
                            }
                            return const SizedBox.shrink();
                          },
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        );
      },
    );
  }

  Future<void> showSaveRequestDialog(BuildContext context, TabModel? activeTab, List<ApiCollection> apiData) async {
    final homeBloc = context.read<HomeBloc>();
    homeBloc.add(LoadApiDataEvent()); // Ensure collections are loaded
    await Future.delayed(Duration(milliseconds: 100)); // Allow state to update

    final requestNameController = TextEditingController(text: activeTab?.tabName ?? '');
    final searchController_saveRequest = TextEditingController();
    String? selectedCollectionName; // Track the selected collection
    String? selectedFolderName; // Track the selected folder
    List<ApiFolder>? currentChildren; // Track the current children (folders or requests)

    final headersBloc = context.read<HeadersBloc>();
    final headers = headersBloc.state.rows
        .where((row) => row.isSelected && row.key.isNotEmpty && row.value.isNotEmpty)
        .fold<Map<String, String>>({}, (map, row) {
      map[row.key] = row.value;
      return map;
    });

    CommonAlertDialogForSaveRequest.show(
      context: context,
      buttonCreateText: "Save",
      title: 'Save Request',
      description: '',
      showNewCollection: context.read<HomeBloc>().state.forCollection,
      inputFields: [
        BlocBuilder<HomeBloc, HomeState>(
          builder: (context, state) {
            final collections = state.apiData;
            final searchQuery = state.searchQuerySaveRequest.toLowerCase();
            final filteredCollections = collections
                .where((collection) =>
                collection.collectionName!.toLowerCase().contains(searchQuery))
                .toList();

            // If a collection is selected, show its folders and requests
            final selectedCollection = collections.firstWhere(
                  (collection) => collection.collectionName == selectedCollectionName,
              orElse: () => ApiCollection(collectionName: null, id: Uuid().v4()),
            );

            // If a folder is selected, show its subfolders and requests
            if (selectedFolderName != null) {
              final selectedFolder = _findFolderRecursive(selectedCollection.children, selectedFolderName!);
              currentChildren = selectedFolder?.children;
            } else {
              currentChildren = selectedCollection.children;
            }

            return Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                CommonTextFormField(
                  labelText: "Request Name",
                  hintTextString: "Enter Request Name",
                  isLabelText: true,
                  isLabelTextBold: true,
                  fontSize: 14,
                  hintStyle: TextStyle(color: AppThemeColor.hintTextColor),
                  labelTextSize: Globals.labelTextSizeForDialog,
                  textEditController: requestNameController,
                  cornerRadius: 4,
                  errorMessage: "",
                ),
                SizedBox(height: 40),
                Row(
                  children: [
                    CommonText(
                      text: 'Save to ',
                      textStyle: mMediumTextStyle16(
                        textSize: 14,
                        textColor: AppThemeColor.white,
                      ),
                    ),
                    if (state.forCollection) ...[
                      CommonText(
                        text: 'Select a collection/folder',
                        underline: false,
                        underlineOnHover: false,
                        underlineColor: AppThemeColor.tabUnselectedTextColor,
                        textStyle: mMediumTextStyle16(
                          textSize: 14,
                          textColor: AppThemeColor.tabUnselectedTextColor,
                        ),
                      ),
                    ] else ...[
                      CommonText(
                        text: 'New Team Workspace ',
                        onTap: () {
                          // Switch back to showing collections
                          context.read<HomeBloc>().add(ToggleForCollectionEvent(true));
                          selectedCollectionName = null;
                          selectedFolderName = null;
                        },
                        underline: false,
                        underlineOnHover: true,
                        underlineColor: AppThemeColor.tabUnselectedTextColor,
                        textStyle: mMediumTextStyle16(
                          textSize: 14,
                          textColor: AppThemeColor.tabUnselectedTextColor,
                        ),
                      ),
                      CommonText(
                        text: '${selectedCollectionName != null ? '/ $selectedCollectionName' : ''} ${selectedFolderName != null ? '/ $selectedFolderName' : ''}',
                        underline: false,
                        underlineColor: AppThemeColor.white,
                        textStyle: mMediumTextStyle16(
                          textSize: 14,
                          textColor: AppThemeColor.white,
                        ),
                      ),
                    ],
                  ],
                ),
                CommonTextFormField(
                  hintTextString: "Search",
                  isLabelText: false,
                  fontSize: 14,
                  padding: EdgeInsets.symmetric(vertical: 10),
                  contentPadding: EdgeInsets.symmetric(horizontal: 10, vertical: 17),
                  hintStyle: TextStyle(color: AppThemeColor.hintTextColor),
                  textEditController: searchController_saveRequest,
                  cornerRadius: Globals.defaultTextInputCornerRadius,
                  isDense: true,
                  errorMessage: "",
                  onChange: (value) {
                    context.read<HomeBloc>().add(SearchQueryChangedEventSaveRequest(value));
                  },
                ),

                // Display collections list or folders/requests
                if (state.forCollection)
                  if(filteredCollections.isNotEmpty)...[
                  SizedBox(
                    height: MediaQuery.of(context).size.height/5,
                    child: ListView.builder(
                      shrinkWrap: true,
                      controller: _scrollControllerCollection,
                      itemCount: filteredCollections.length,
                      itemBuilder: (context, index) {
                        final collection = filteredCollections[index];
                        return Material(
                          type: MaterialType.transparency,
                          child: ListTile(
                            hoverColor: Colors.grey.withOpacity(0.1),
                            enabled: true,
                            contentPadding: EdgeInsets.only(left: 0),
                            visualDensity: VisualDensity(horizontal: 0, vertical: -4),
                            minVerticalPadding: 0,
                            title: Row(
                              mainAxisAlignment: MainAxisAlignment.start,
                              crossAxisAlignment: CrossAxisAlignment.center,
                              children: [
                                const CommonSVGIcon(
                                  imageName: 'collection',
                                  imagePath: 'images',
                                  color: AppThemeColor.white,
                                  height: 20,
                                  width: 20,
                                ),
                                SizedBox(width: 10),
                                CommonText(
                                  text: collection.collectionName ?? 'Unnamed Collection',
                                  textStyle: mMediumTextStyle16(
                                    textSize: 14,
                                    textColor: AppThemeColor.white,
                                  ),
                                ),
                              ],
                            ),
                            onTap: () {
                              // Set the selected collection and update forCollection
                              selectedCollectionName = collection.collectionName;
                              context.read<HomeBloc>().add(ToggleForCollectionEvent(false));
                              homeBloc.add(LoadApiDataEvent());
                            },
                          ),
                        );
                      },
                    ),
                  )] else...[
                    SizedBox.shrink()
                  ]
                  else
                  SizedBox(
                    height: MediaQuery.of(context).size.height/5,
                    child: SingleChildScrollView(
                      controller: _scrollControllerFolder,
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          // Show folders and requests for the selected collection or folder
                          ...(currentChildren ?? []).map((child) {
                            return Material(
                              type: MaterialType.transparency,
                              child: ListTile(
                                hoverColor: Colors.grey.withOpacity(0.1),
                                enabled: true,
                                contentPadding: EdgeInsets.only(left: 0),
                                visualDensity: VisualDensity(horizontal: 0, vertical: -4),
                                minVerticalPadding: 0,
                                title: Row(
                                  mainAxisAlignment: MainAxisAlignment.start,
                                  crossAxisAlignment: CrossAxisAlignment.center,
                                  children: [
                                    if (child.folderName != null) ...[
                                      const CommonSVGIcon(
                                        imageName: 'folder2',
                                        imagePath: 'images',
                                        color: AppThemeColor.white,
                                        height: 24,
                                        width: 24,
                                      ),
                                    ] else ...[
                                      const Icon(
                                        Icons.api,
                                        color: Colors.white,
                                        size: 20,
                                      ),
                                    ],
                                    SizedBox(width: 10),
                                    CommonText(
                                      text: child.folderName ?? child.requestData ?? 'Unnamed',
                                      textStyle: mMediumTextStyle16(
                                        textSize: 14,
                                        textColor: AppThemeColor.white,
                                      ),
                                    ),
                                  ],
                                ),
                                onTap: () {
                                  if (child.folderName != null) {
                                    // Navigate into the folder
                                    selectedFolderName = child.folderName;
                                    currentChildren = child.children;
                                    homeBloc.add(LoadApiDataEvent()); // Trigger UI rebuild
                                  } else {
                                    // Save the request to this folder or collection
                                    selectedFolderName = null;
                                  }
                                },
                              ),
                            );
                          }).toList(),
                        ],
                      ),
                    ),
                  ),
              ],
            );
          },
        ),
      ],
      onCancel: () {
        Navigator.of(context).pop();
      },

      onCreate: () async {
        if (activeTab != null && selectedCollectionName != null) {
          final homeBloc = context.read<HomeBloc>();
          final postmanRequestBloc = context.read<PostmanRequestBloc>();

          // Find the selected collection to get its ID
          final collections = homeBloc.state.apiData;
          final selectedCollection = collections.firstWhere(
            (c) => c.collectionName == selectedCollectionName,
            orElse: () => ApiCollection(collectionName: null, id: Uuid().v4()),
          );

          // Get the collection ID from the API collection model
          final collectionId = selectedCollection.id;

          // Get the headers from the HeadersBloc
          final headersBloc = context.read<HeadersBloc>();
          final headers = headersBloc.state.rows
              .where((row) => row.isSelected && row.key.isNotEmpty)
              .fold<Map<String, String>>({}, (map, row) {
            map[row.key] = row.value;
            return map;
          });

          // Prepare the body data
          final bodyData = {
            "type": "json",
            "content": jsonEncode(activeTab.jsonData ?? {})
          };

          // Call the saveRequest API
          postmanRequestBloc.add(
            SaveRequest(
              name: requestNameController.text,
              url: activeTab.url ?? "",
              method: activeTab.method,
              headers: headers,
              body: bodyData,
              params: {}, // Empty params for now
              auth: {"type": "none"}, // Default auth type
              collectionId: collectionId, // Use the selected collection ID
              tabUuid: activeTab.uuid,
            ),
          );

          // Update the TabModel in openTabs
          final updatedTabs = homeBloc.state.openTabs.map((tab) {
            if (tab.uuid == activeTab.uuid) {
              return tab.copyWith(
                tabName: requestNameController.text,
                headers: activeTab.headers ?? headers,
                isModified: false,
              );
            }
            return tab;
          }).toList();

          // Emit the updated state
          homeBloc.add(UpdateDialogTabEvent(updatedTabs));

          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('Request saved successfully!')),
          );
          homeBloc.add(LoadApiDataEvent());
        } else {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('Please select a collection to save the request.')),
          );
        }
        Navigator.of(context).pop();
      },


      onTapNewFolder: () {
        _showAddFolderDialog(context, selectedCollectionName, selectedFolderName);
      },
      onTapNewCollection: () {
        _showAddCollectionDialog(context);
      },
    );
  }

  void _showAddFolderDialog(BuildContext context, String? selectedCollectionName, String? selectedFolderName) {
    final folderNameController = TextEditingController();

    CommonAlertDialog.show(
      context: context,
      buttonCreateText: "Create",
      title: 'New Folder',
      description: 'Create a new folder to organize your API requests.',
      inputFields: [
        CommonTextFormField(
          labelText: "Enter Folder Name",
          hintTextString: "Enter Folder Name",
          isLabelText: true,
          isLabelTextBold: true,
          fontSize: 14,
          hintStyle: TextStyle(color: AppThemeColor.hintTextColor),
          labelTextSize: Globals.labelTextSizeForDialog,
          textEditController: folderNameController,
          cornerRadius: 4,
          errorMessage: "",
        ),
      ],
      onCancel: () {
        Navigator.of(context).pop();
      },
      onCreate: () async {
        final folderName = folderNameController.text;
        if (folderName.isNotEmpty && selectedCollectionName != null) {
          try {
            homeBloc.add(
              AddFolderEvent(
                selectedCollectionName,
                folderName,
                parentFolderName: selectedFolderName,
              ),
            );
            await Future.delayed(Duration(milliseconds: 100));
            homeBloc.add(LoadApiDataEvent());
          } catch (e) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(content: Text(e.toString())),
            );
          }
        }
        Navigator.of(context).pop();

        Future.delayed(Duration(milliseconds: 300), () {
          if (_scrollControllerFolder.hasClients) {
            _scrollControllerFolder.animateTo(
              _scrollControllerFolder.position.maxScrollExtent,
              duration: Duration(milliseconds: 500),
              curve: Curves.easeOut,
            );
          }
        });
      },
    );
  }

  void _showAddCollectionDialog(BuildContext context) {
    final collectionNameController = TextEditingController();

    CommonAlertDialog.show(
      context: context,
      buttonCreateText: "Create",
      title: 'New Collection',
      description: 'Create a new collection to manage your API requests.',
      inputFields: [
        CommonTextFormField(
          labelText: "Enter Collection Name",
          hintTextString: "Enter Collection Name",
          isLabelText: true,
          isLabelTextBold: true,
          fontSize: 14,
          hintStyle: TextStyle(color: AppThemeColor.hintTextColor),
          labelTextSize: Globals.labelTextSizeForDialog,
          textEditController: collectionNameController,
          cornerRadius: 4,
          errorMessage: "",
        ),
      ],
      onCancel: () {
        Navigator.of(context).pop();
      },
      onCreate: () {
        final collectionName = collectionNameController.text;
        if (collectionName.isNotEmpty) {
          homeBloc.add(AddCollectionEvent(collectionName));
          homeBloc.add(LoadApiDataEvent());
          Navigator.of(context).pop();
          Future.delayed(Duration(milliseconds: 300), () {
            if (_scrollControllerCollection.hasClients) {
              _scrollControllerCollection.animateTo(
                _scrollControllerCollection.position.maxScrollExtent,
                duration: Duration(milliseconds: 500),
                curve: Curves.easeOut,
              );
            }
          });
        }

      },
    );
  }

  ApiFolder? _findFolderRecursive(List<ApiFolder>? folders, String folderName) {
    if (folders == null) return null;

    for (final folder in folders) {
      if (folder.folderName == folderName) {
        return folder;
      }
      final found = _findFolderRecursive(folder.children, folderName);
      if (found != null) {
        return found;
      }
    }
    return null;
  }
}

void saveRequestToDB(TabModel? activeTab, BuildContext context) {
  if (activeTab != null) {
    final headersBloc = context.read<HeadersBloc>();
    final headers = headersBloc.state.rows.where((row) => row.isSelected && row.key.isNotEmpty).fold<Map<String, String>>({}, (map, row) {
      map[row.key] = row.value;
      return map;
    });

    final bodyData = {
      "type": "json",
      "content": jsonEncode(activeTab.jsonData)
    };

    print('bodyData while saving: $bodyData');


    context.read<PostmanRequestBloc>().add(
      SaveRequest(
        name: activeTab.tabName,
        url: activeTab.url ?? "",
        method: activeTab.method,
        headers: headers,
        body: bodyData,
        params: {}, // Empty params for now
        auth: {"type": "none"}, // Default auth type
        collectionId: CommonConstants.collectionID.toString(),
        tabUuid: activeTab.uuid,
      ),
    );
  }
}

extension IterableExtension<T> on Iterable<T> {
  T? firstWhereOrNull(bool Function(T) test) {
    for (var element in this) {
      if (test(element)) {
        return element;
      }
    }
    return null;
  }
}


class ScrollControllerProvider extends InheritedWidget {
  final ScrollController scrollControllerTabbar;

  const ScrollControllerProvider({
    Key? key,
    required this.scrollControllerTabbar,
    required Widget child,
  }) : super(key: key, child: child);

  static ScrollControllerProvider? of(BuildContext context) {
    return context.dependOnInheritedWidgetOfExactType<ScrollControllerProvider>();
  }

  @override
  bool updateShouldNotify(ScrollControllerProvider oldWidget) {
    return oldWidget.scrollControllerTabbar != scrollControllerTabbar;
  }
}










