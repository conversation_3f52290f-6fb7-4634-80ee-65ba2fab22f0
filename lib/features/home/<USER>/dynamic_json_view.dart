import 'dart:convert';
import 'package:flutter/material.dart';

class DynamicJsonView extends StatefulWidget {
  final dynamic jsonData;
  final String? jsonString;
  final bool isDarkMode;
  final String? searchQuery;
  final List<int>? searchMatches;
  final int? currentMatchIndex;

  const DynamicJsonView({
    super.key,
    this.jsonData,
    this.jsonString,
    this.isDarkMode = true,
    this.searchQuery,
    this.searchMatches,
    this.currentMatchIndex,
  });

  @override
  State<DynamicJsonView> createState() => _DynamicJsonViewState();
}

class _DynamicJsonViewState extends State<DynamicJsonView> {
  late List<String> _lines;
  final ScrollController _scrollController = ScrollController();

  // Constants for virtualization
  static const double _itemExtent = 18.0; // Height of each line in pixels
  static const double _overscanCount = 20.0; // Number of items to render beyond visible area

  @override
  void initState() {
    super.initState();
    _parseJsonToLines();
  }

  @override
  void didUpdateWidget(DynamicJsonView oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.jsonData != widget.jsonData ||
        oldWidget.jsonString != widget.jsonString) {
      _parseJsonToLines();
    }
  }

  void _parseJsonToLines() {
    String jsonStr = widget.jsonString ?? '';

    if (widget.jsonData != null) {
      // Format the JSON data with indentation
      try {
        // First set a loading state
        setState(() {
          _lines = ['Loading JSON data...'];
        });

        // Use a microtask to avoid blocking the UI
        Future.microtask(() {
          try {
            // For small JSON, process directly
            final jsonString = const JsonEncoder.withIndent('  ').convert(widget.jsonData);
            final lines = jsonString.split('\n');

            if (mounted) {
              setState(() {
                _lines = lines;
              });
            }
          } catch (e) {
            if (mounted) {
              setState(() {
                _lines = ['Error formatting JSON: $e'];
              });
            }
          }
        });
      } catch (e) {
        _lines = ['Error formatting JSON: $e'];
      }
    } else if (jsonStr.isNotEmpty) {
      _lines = jsonStr.split('\n');
    } else {
      _lines = ['No data available'];
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scrollbar(
      controller: _scrollController,
      child: ListView.builder(
        controller: _scrollController,
        itemCount: _lines.length,
        itemExtent: _itemExtent, // Fixed height for each item improves performance
        cacheExtent: _itemExtent * _overscanCount, // Cache items beyond visible area
        itemBuilder: (context, index) {
          if (index >= _lines.length) return const SizedBox.shrink();

          final line = _lines[index];

          return Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Line number
              SizedBox(
                width: 35,
                child: Text(
                  '${index + 1}',
                  style: TextStyle(
                    color: widget.isDarkMode ? Colors.grey : Colors.grey.shade700,
                    fontSize: 11,
                    fontFamily: 'monospace',
                  ),
                  textAlign: TextAlign.right,
                ),
              ),
              const SizedBox(width: 6),
              // Line content with syntax highlighting and search highlighting
              Expanded(
                child: _buildHighlightedText(line, index),
              ),
            ],
          );
        },
      ),
    );
  }

  Widget _buildHighlightedText(String line, int lineIndex) {
    if (widget.searchQuery == null || widget.searchQuery!.isEmpty) {
      return Text(
        line,
        style: TextStyle(
          color: _getColorForLine(line),
          fontSize: 12,
          fontFamily: 'monospace',
        ),
      );
    }

    // Calculate the character position for this line
    int lineStartPosition = 0;
    for (int i = 0; i < lineIndex; i++) {
      lineStartPosition += _lines[i].length + 1; // +1 for newline character
    }

    final List<TextSpan> spans = [];
    final searchQuery = widget.searchQuery!.toLowerCase();
    int lastIndex = 0;

    // Find all matches in this line
    final lineMatches = <int>[];
    if (widget.searchMatches != null) {
      for (final matchPosition in widget.searchMatches!) {
        if (matchPosition >= lineStartPosition &&
            matchPosition < lineStartPosition + line.length) {
          lineMatches.add(matchPosition - lineStartPosition);
        }
      }
    }

    // Build text spans with highlighting
    for (final matchIndex in lineMatches) {
      // Add text before the match
      if (matchIndex > lastIndex) {
        spans.add(TextSpan(
          text: line.substring(lastIndex, matchIndex),
          style: TextStyle(
            color: _getColorForLine(line),
            fontSize: 12,
            fontFamily: 'monospace',
          ),
        ));
      }

      // Add highlighted match
      final isCurrentMatch = widget.currentMatchIndex != null &&
          widget.searchMatches != null &&
          widget.searchMatches!.isNotEmpty &&
          widget.searchMatches![widget.currentMatchIndex!] == lineStartPosition + matchIndex;

      spans.add(TextSpan(
        text: line.substring(matchIndex, matchIndex + searchQuery.length),
        style: TextStyle(
          color: Colors.black,
          fontSize: 12,
          backgroundColor: isCurrentMatch ? Colors.orange : Colors.yellow,
          fontFamily: 'monospace',
        ),
      ));

      lastIndex = matchIndex + searchQuery.length;
    }

    // Add remaining text
    if (lastIndex < line.length) {
      spans.add(TextSpan(
        text: line.substring(lastIndex),
        style: TextStyle(
          color: _getColorForLine(line),
          fontSize: 12,
          fontFamily: 'monospace',
        ),
      ));
    }

    return RichText(
      text: TextSpan(children: spans),
    );
  }

  Color _getColorForLine(String line) {
    final trimmedLine = line.trim();

    if (widget.isDarkMode) {
      // Dark mode colors
      if (trimmedLine.startsWith('"') && trimmedLine.endsWith('",')) {
        return Colors.green; // String keys
      } else if (trimmedLine.contains('": "')) {
        return Colors.lightBlue; // String values
      } else if (trimmedLine.contains('": ') &&
                (trimmedLine.contains('true') || trimmedLine.contains('false'))) {
        return Colors.orange; // Boolean values
      } else if (trimmedLine.contains('": ') &&
                RegExp(r'": \d').hasMatch(trimmedLine)) {
        return Colors.purple.shade300; // Number values
      } else if (trimmedLine.contains('{') || trimmedLine.contains('}') ||
                trimmedLine.contains('[') || trimmedLine.contains(']')) {
        return Colors.white; // Brackets
      }
      return Colors.grey.shade300; // Default color
    } else {
      // Light mode colors
      if (trimmedLine.startsWith('"') && trimmedLine.endsWith('",')) {
        return Colors.green.shade800; // String keys
      } else if (trimmedLine.contains('": "')) {
        return Colors.blue.shade800; // String values
      } else if (trimmedLine.contains('": ') &&
                (trimmedLine.contains('true') || trimmedLine.contains('false'))) {
        return Colors.orange.shade800; // Boolean values
      } else if (trimmedLine.contains('": ') &&
                RegExp(r'": \d').hasMatch(trimmedLine)) {
        return Colors.purple.shade800; // Number values
      } else if (trimmedLine.contains('{') || trimmedLine.contains('}') ||
                trimmedLine.contains('[') || trimmedLine.contains(']')) {
        return Colors.black; // Brackets
      }
      return Colors.grey.shade800; // Default color
    }
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }
}
